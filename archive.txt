                if False:
                    # Download process
                    from utils.export import get_csv_syntax
                    csv_data = get_csv_syntax(movie_list.movies)
                    download_filename = movie_list.slug + '.csv'

                    download_button = st.download_button(
                        label="Download CSV",
                        data=csv_data,
                        file_name=download_filename,
                        mime='text/csv'
                    )

                    if download_button:
                        st.success(f'{download_filename} downloaded.')
                        notifier.send(f'List downlaoded: {processed_input}')