# Update Virtual Environment Script

# Deactivate current venv if active
if ($env:VIRTUAL_ENV) {
    deactivate
}

# Remove existing venv
if (Test-Path "venv") {
    Remove-Item -Recurse -Force "venv"
}

# Create new venv
python -m venv venv

# Activate new venv
.\venv\Scripts\activate

# Install requirements
pip install -r requirements.txt

Write-Host "Virtual environment updated successfully!" -ForegroundColor Green
