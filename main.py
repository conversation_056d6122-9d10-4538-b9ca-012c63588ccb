import pandas as pd
import streamlit as st
from letterboxdpy.core.scraper import parse_url
from letterboxdpy.user import User
from letterboxdpy.list import List

from models.checker import Checker
from models.config import Page
from models.manager import Input
from models.movie_list import MovieList
from models.notifier import Notifier
from models.parser.utils import catch_error_message
from models.url import Url
from models.url.helpers import convert_to_pattern

def list_mode(processed_input):
    st.write(f"DEBUG: list_mode called with URL: {processed_input}")

    try:
        st.write("DEBUG: Parsing URL...")
        url_dom = parse_url(processed_input)
        st.write("DEBUG: URL parsed successfully")

        st.write("DEBUG: Checking for error messages...")
        err_msg = catch_error_message(url_dom)
        st.write(f"DEBUG: Error message: {err_msg}")

        st.write("DEBUG: Creating checker...")
        checker = Checker(url_dom)
        is_list = checker.is_list()
        st.write(f"DEBUG: Is list: {is_list}")

        if err_msg:
            st.error(f'{err_msg}', icon='👀')
            if not is_list:
                st.warning(f'The address is not a Letterboxd list.', icon='💡')
                st.warning('Please enter a valid **username**, **list url** or **username/list-title.**', icon='💡')
            st.stop()

        st.write("DEBUG: Getting list metadata...")
        list_meta = checker.get_list_meta(processed_input)
        st.write(f"DEBUG: List meta: {list_meta}")

    except Exception as e:
        st.error(f"DEBUG: Error in list_mode: {str(e)}")
        st.write(f"DEBUG: Exception details: {e}")
        return

    try:
        st.write("DEBUG: Creating MovieList object...")
        # address is list, so we can create the object now
        movie_list = MovieList(
            Url(
                list_meta['url'],
                url_dom
            )
        )
        st.write("DEBUG: MovieList created successfully")

        st.write("DEBUG: Getting movie list...")
        if list_meta['is_available']:
            st.write("DEBUG: List is available, getting movies...")
            movies = movie_list.movies
            st.write(f"DEBUG: Found {len(movies)} movies")

            st.dataframe(
                pd.DataFrame(
                    movies,
                    columns=["Rank", "Year", "Title", "LetterboxdURI"]
                ),
                hide_index=True,
                use_container_width=True,
            )
            st.write("DEBUG: Dataframe displayed successfully")
        else:
            st.warning('List is not available.')

    except Exception as e:
        st.error(f"DEBUG: Error creating MovieList or displaying data: {str(e)}")
        st.write(f"DEBUG: Exception details: {e}")


if __name__ == "__main__":
    page = Page()
    page.create_title()
    page.create_footer()
    
    user_input = Input()
    user_input.process_data()
 
    if not user_input.data:
        st.write('_Awaiting input.._')
        st.stop()

    if user_input.is_username:
        username = user_input.data # username

        try:
            user_instance = User(username)
            user_lists = user_instance.get_lists()
        except Exception:
            st.error(f"User '{username}' not found or could not be accessed.")
            st.stop()

        # Display user lists
        if user_lists and 'lists' in user_lists and user_lists['lists']:
            st.write(f"Found {user_lists['count']} lists")

            # Create list options for selectbox
            list_options = {}
            for list_id, list_data in user_lists['lists'].items():
                display_name = f"{list_data['title']} ({list_data['count']} movies)"
                list_options[display_name] = list_data['url']

            selected_list = st.selectbox(
                "Select a list:",
                options=list(list_options.keys()),
                index=0
            )

            # Show selected list details and download button
            if selected_list:
                selected_url = list_options[selected_list]

                # Find selected list data
                selected_list_data = None
                for list_data in user_lists['lists'].values():
                    if list_data['url'] == selected_url:
                        selected_list_data = list_data
                        break

                if selected_list_data:
                    # Show list details
                    col1, col2, col3 = st.columns(3)

                    with col1:
                        st.metric("Movies", selected_list_data['count'])
                    with col2:
                        st.metric("Likes", selected_list_data['likes'])
                    with col3:
                        st.metric("Comments", selected_list_data['comments'])

                    # Show description if available
                    if selected_list_data.get('description'):
                        st.caption(f"Description: {selected_list_data['description']}")

                # Automatically show list movies
                st.divider()
                st.write(f"Loading movies from: {selected_url}")
                try:
                    list_mode(selected_url)
                except Exception as e:
                    st.error(f"Error loading list: {str(e)}")
                    st.write("Debug info:", e)
        else:
            st.write("No lists found for this user.")

        # Username modunda işlem tamamlandı, devam etme
        st.stop()
    else:
        if user_input.is_short_url:
            processed_input = user_input.data.replace('/detail', '')
        else:
            processed_input = convert_to_pattern(user_input.data)
            processed_input = user_input.convert_to_url(processed_input)
        list_mode(processed_input)

    if not processed_input:
        st.warning('Please enter a valid **username**, **list url** or **username/list-title.**', icon='💡')
        st.stop()