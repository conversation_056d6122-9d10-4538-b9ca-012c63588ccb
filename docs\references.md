# Project References

## Streamlit API
- **st.set_page_config()** - https://docs.streamlit.io/develop/api-reference/configuration/st.set_page_config
- **st.markdown()** - https://docs.streamlit.io/develop/api-reference/text/st.markdown
- **st.title()** - https://docs.streamlit.io/develop/api-reference/text/st.title
- **st.write()** - https://docs.streamlit.io/develop/api-reference/write-magic/st.write
- **st.caption()** - https://docs.streamlit.io/develop/api-reference/text/st.caption
- **st.text_input()** - https://docs.streamlit.io/develop/api-reference/widgets/st.text_input
- **st.selectbox()** - https://docs.streamlit.io/develop/api-reference/widgets/st.selectbox
- **st.button()** - https://docs.streamlit.io/develop/api-reference/widgets/st.button
- **st.columns()** - https://docs.streamlit.io/develop/api-reference/layout/st.columns
- **st.expander()** - https://docs.streamlit.io/develop/api-reference/layout/st.expander
- **st.divider()** - https://docs.streamlit.io/develop/api-reference/layout/st.divider
- **st.success()** - https://docs.streamlit.io/develop/api-reference/status/st.success
- **st.error()** - https://docs.streamlit.io/develop/api-reference/status/st.error
- **st.warning()** - https://docs.streamlit.io/develop/api-reference/status/st.warning
- **st.info()** - https://docs.streamlit.io/develop/api-reference/status/st.info
- **st.dataframe()** - https://docs.streamlit.io/develop/api-reference/data/st.dataframe
- **st.metric()** - https://docs.streamlit.io/develop/api-reference/data/st.metric
- **st.json()** - https://docs.streamlit.io/develop/api-reference/data/st.json
- **st.stop()** - https://docs.streamlit.io/develop/api-reference/execution-flow/st.stop
- **st.spinner()** - https://docs.streamlit.io/develop/api-reference/status/st.spinner
- **st.progress()** - https://docs.streamlit.io/develop/api-reference/status/st.progress

## Python Standard Library
- **dataclasses** - https://docs.python.org/3/library/dataclasses.html
- **typing** - https://docs.python.org/3/library/typing.html
- **typing_extensions** - https://typing-extensions.readthedocs.io/en/latest/
- **os.path.join()** - https://docs.python.org/3/library/os.path.html#os.path.join
- **json** - https://docs.python.org/3/library/json.html
- **time** - https://docs.python.org/3/library/time.html
- **csv** - https://docs.python.org/3/library/csv.html
- **io.StringIO** - https://docs.python.org/3/library/io.html#io.StringIO

## Web Technologies
- **CSS Linear Gradients** - https://developer.mozilla.org/en-US/docs/Web/CSS/gradient/linear-gradient
- **CSS Selectors** - https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_selectors
- **HTML `<style>` tag** - https://developer.mozilla.org/en-US/docs/Web/HTML/Element/style

## Third-Party Libraries
- **pandas.DataFrame** - https://pandas.pydata.org/docs/reference/api/pandas.DataFrame.html
- **BeautifulSoup** - https://www.crummy.com/software/BeautifulSoup/bs4/doc/
- **requests** - https://docs.python-requests.org/en/latest/api/
- **stqdm** - https://github.com/Wirg/stqdm
- **letterboxdpy** - https://github.com/fastfingertips/letterboxdpy
- **lxml** - https://lxml.de/

## Development
- **Conventional Commits** - https://www.conventionalcommits.org/
- **Streamlit Cloud** - https://docs.streamlit.io/deploy/streamlit-cloud
