# Project References

This document contains all the official documentation links and references used throughout the project.

## 📚 Streamlit API References

### Configuration
- **st.set_page_config()** - https://docs.streamlit.io/develop/api-reference/configuration/st.set_page_config
  - Used in: `models/config.py`
  - Purpose: Configure page layout, title, icon, and menu items

### Text & Display
- **st.markdown()** - https://docs.streamlit.io/develop/api-reference/text/st.markdown
  - Used in: `models/config.py`, `main.py`
  - Purpose: Render markdown text and inject HTML/CSS

- **st.title()** - https://docs.streamlit.io/develop/api-reference/text/st.title
  - Used in: `models/config.py`
  - Purpose: Display page title

- **st.write()** - https://docs.streamlit.io/develop/api-reference/write-magic/st.write
  - Used in: `main.py`
  - Purpose: Display text, data, and other content

- **st.caption()** - https://docs.streamlit.io/develop/api-reference/text/st.caption
  - Used in: `main.py`
  - Purpose: Display small text with muted styling

### Input Widgets
- **st.text_input()** - https://docs.streamlit.io/develop/api-reference/widgets/st.text_input
  - Used in: `models/manager.py`
  - Purpose: Get text input from user

- **st.selectbox()** - https://docs.streamlit.io/develop/api-reference/widgets/st.selectbox
  - Used in: `main.py`
  - Purpose: Select from dropdown options

- **st.button()** - https://docs.streamlit.io/develop/api-reference/widgets/st.button
  - Used in: `main.py`
  - Purpose: Create clickable buttons

### Layout & Containers
- **st.columns()** - https://docs.streamlit.io/develop/api-reference/layout/st.columns
  - Used in: `main.py`
  - Purpose: Create side-by-side columns

- **st.expander()** - https://docs.streamlit.io/develop/api-reference/layout/st.expander
  - Used in: `main.py`
  - Purpose: Create expandable/collapsible sections

- **st.divider()** - https://docs.streamlit.io/develop/api-reference/layout/st.divider
  - Used in: `main.py`
  - Purpose: Add visual separator line

### Status Elements
- **st.success()** - https://docs.streamlit.io/develop/api-reference/status/st.success
  - Used in: `main.py`
  - Purpose: Display success messages

- **st.error()** - https://docs.streamlit.io/develop/api-reference/status/st.error
  - Used in: `main.py`
  - Purpose: Display error messages

- **st.warning()** - https://docs.streamlit.io/develop/api-reference/status/st.warning
  - Used in: `main.py`
  - Purpose: Display warning messages

- **st.info()** - https://docs.streamlit.io/develop/api-reference/status/st.info
  - Used in: `main.py`
  - Purpose: Display informational messages

### Data Display
- **st.dataframe()** - https://docs.streamlit.io/develop/api-reference/data/st.dataframe
  - Used in: `main.py`
  - Purpose: Display interactive dataframes

- **st.metric()** - https://docs.streamlit.io/develop/api-reference/data/st.metric
  - Used in: `main.py`
  - Purpose: Display metrics with labels and values

- **st.json()** - https://docs.streamlit.io/develop/api-reference/data/st.json
  - Used in: `main.py`
  - Purpose: Display JSON data

### Control Flow
- **st.stop()** - https://docs.streamlit.io/develop/api-reference/execution-flow/st.stop
  - Used in: `main.py`
  - Purpose: Stop execution of the script

- **st.spinner()** - https://docs.streamlit.io/develop/api-reference/status/st.spinner
  - Used in: `main.py`
  - Purpose: Display loading spinner

### Progress
- **st.progress()** - https://docs.streamlit.io/develop/api-reference/status/st.progress
  - Used in: `models/movie_list.py`
  - Purpose: Display progress bar

## 🐍 Python Standard Library

### Data Classes
- **dataclasses** - https://docs.python.org/3/library/dataclasses.html
  - Used in: `models/config.py`, `models/selectors.py`
  - Purpose: Create classes with automatic special methods

### Type Hints
- **typing.Dict** - https://docs.python.org/3/library/typing.html#typing.Dict
  - Used in: `models/config.py`, `models/selectors.py`
  - Purpose: Type hints for dictionaries

- **typing.Optional** - https://docs.python.org/3/library/typing.html#typing.Optional
  - Used in: `models/config.py`
  - Purpose: Type hints for optional values

- **typing.Union** - https://docs.python.org/3/library/typing.html#typing.Union
  - Used in: `models/parser/utils.py`
  - Purpose: Type hints for union types

- **typing_extensions.TypeAlias** - https://docs.python.org/3/library/typing.html#type-aliases
  - Used in: `models/selectors.py`
  - Purpose: Create type aliases

### File Operations
- **os.path.join()** - https://docs.python.org/3/library/os.path.html#os.path.join
  - Used in: `models/config.py`
  - Purpose: Join file paths in OS-independent way

### Data Structures
- **tuple** - https://docs.python.org/3/library/stdtypes.html#tuple
  - Used in: `models/selectors.py`
  - Purpose: Immutable sequences

## 🌐 Web Technologies

### CSS
- **Linear Gradients** - https://developer.mozilla.org/en-US/docs/Web/CSS/gradient/linear-gradient
  - Used in: `models/config.py`
  - Purpose: Create gradient backgrounds

- **CSS Selectors** - https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_selectors
  - Used in: `models/selectors.py`
  - Purpose: Select HTML elements for styling

### HTML
- **`<style>` tag** - https://developer.mozilla.org/en-US/docs/Web/HTML/Element/style
  - Used in: `models/config.py`
  - Purpose: Embed CSS styles in HTML

## 📦 Third-Party Libraries

### Pandas
- **pandas.DataFrame** - https://pandas.pydata.org/docs/reference/api/pandas.DataFrame.html
  - Used in: `main.py`
  - Purpose: Data manipulation and analysis

### BeautifulSoup
- **BeautifulSoup.find()** - https://www.crummy.com/software/BeautifulSoup/bs4/doc/#find
  - Used in: `models/movie_list.py`, `models/parser/`
  - Purpose: Parse and search HTML documents

- **BeautifulSoup.find_all()** - https://www.crummy.com/software/BeautifulSoup/bs4/doc/#find-all
  - Used in: `models/movie_list.py`
  - Purpose: Find all matching HTML elements

### Requests
- **requests.get()** - https://docs.python-requests.org/en/latest/api/#requests.get
  - Used in: `models/notifier.py`
  - Purpose: Make HTTP GET requests

- **requests.post()** - https://docs.python-requests.org/en/latest/api/#requests.post
  - Used in: `models/notifier.py`
  - Purpose: Make HTTP POST requests

### STQDM
- **stqdm** - https://github.com/Wirg/stqdm
  - Used in: `models/movie_list.py`
  - Purpose: Progress bars for Streamlit

### LetterboxdPy
- **letterboxdpy.user.User** - https://github.com/fastfingertips/letterboxdpy
  - Used in: `main.py`
  - Purpose: Interact with Letterboxd user data

- **letterboxdpy.core.scraper.parse_url** - https://github.com/fastfingertips/letterboxdpy
  - Used in: `main.py`, `models/movie_list.py`
  - Purpose: Parse Letterboxd URLs

## 🔧 Development Tools

### Git
- **Conventional Commits** - https://www.conventionalcommits.org/
  - Used in: Project commits
  - Purpose: Standardized commit message format

### Streamlit Deployment
- **Streamlit Cloud** - https://docs.streamlit.io/deploy/streamlit-cloud
  - Used in: Project deployment
  - Purpose: Deploy Streamlit apps to the cloud

## 📋 Project Structure References

### Configuration Patterns
- **Streamlit Configuration Best Practices** - https://docs.streamlit.io/develop/concepts/configuration
- **Python Project Structure** - https://docs.python-guide.org/writing/structure/

### Error Handling
- **Python Exception Handling** - https://docs.python.org/3/tutorial/errors.html
- **Streamlit Error Handling** - https://docs.streamlit.io/develop/concepts/design/error-handling
